package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DEFAULT_KEY;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * This class contains the role based access configuration based on the workflow and crud operation
 * rbac:
 *   definition:
 *     policies:
 *       approval:
 *         permissions:
 *           DELETE:
 *             - Intuit.sb.accountant.masteradmin
 *             - MMa
 *             - Intuit.sb.accountant.companyadmin
 *             - CAa
 */

@Configuration
@ConfigurationProperties(prefix = "rbac.definition")
@Getter
@Setter
public class DefinitionRbacConfig {
  private boolean enabled = false;
  private boolean providerLevelEnabled = true;
  private Map<String, AccessPolicy> policies = new HashMap<>();
  private String genericPolicyResource = "Intuit.authz.rbac.commonv3";

  /**
   * Retrieves the roles for a specific workflow and CRUD operation as a comma-separated string,
   * defaults to 'default' workflow if the provided workflow is not present or is null.
   * @param workflowType The requested workflow type (e.g., 'default', 'approval')
   * @param operation The CRUD operation (e.g., 'DELETE')
   * @return A string containing the roles, comma-separated, or an empty string if no roles found.
   */

  public String getRolesAsString(String workflowType, String operation) {
    return Optional.ofNullable(policies)
        .map(p -> p.getOrDefault(workflowType, p.get(DEFAULT_KEY)))
        .map(AccessPolicy::getPermissions)
        .map(permission -> permission.get(operation))
        .map(roles -> String.join(",", roles))
        .orElse("");
  }
}

